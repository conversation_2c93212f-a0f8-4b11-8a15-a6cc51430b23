import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as ss from 'simple-statistics';
import * as moment from 'moment';

/**
 * 设备健康状态枚举
 */
export enum HealthStatus {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
  CRITICAL = 'critical'
}

/**
 * 故障类型枚举
 */
export enum FailureType {
  MECHANICAL = 'mechanical',
  ELECTRICAL = 'electrical',
  HYDRAULIC = 'hydraulic',
  PNEUMATIC = 'pneumatic',
  SOFTWARE = 'software',
  SENSOR = 'sensor',
  WEAR = 'wear',
  OVERHEATING = 'overheating'
}

/**
 * 维护类型枚举
 */
export enum MaintenanceType {
  PREVENTIVE = 'preventive',
  PREDICTIVE = 'predictive',
  CORRECTIVE = 'corrective',
  EMERGENCY = 'emergency'
}

/**
 * 设备健康数据接口
 */
export interface DeviceHealthData {
  deviceId: string;
  timestamp: Date;
  temperature: number;
  vibration: number;
  pressure: number;
  humidity: number;
  voltage: number;
  current: number;
  speed: number;
  torque: number;
  efficiency: number;
  errorCount: number;
  operatingHours: number;
  maintenanceHistory: number;
}

/**
 * 健康评分接口
 */
export interface HealthScore {
  deviceId: string;
  score: number;
  status: HealthStatus;
  timestamp: Date;
  factors: {
    temperature: number;
    vibration: number;
    pressure: number;
    electrical: number;
    mechanical: number;
    overall: number;
  };
  recommendations: string[];
}

/**
 * 故障预测接口
 */
export interface FailurePrediction {
  deviceId: string;
  failureType: FailureType;
  probability: number;
  timeToFailure: number; // 小时
  confidence: number;
  timestamp: Date;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * 维护建议接口
 */
export interface MaintenanceRecommendation {
  deviceId: string;
  type: MaintenanceType;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  description: string;
  estimatedDuration: number; // 小时
  estimatedCost: number;
  requiredParts: string[];
  requiredSkills: string[];
  scheduledDate: Date;
  deadline: Date;
  riskIfDelayed: string;
}

@Injectable()
export class MaintenanceService {
  private readonly logger = new Logger(MaintenanceService.name);
  
  // 健康评分缓存
  private healthScores: Map<string, HealthScore> = new Map();
  private predictions: Map<string, FailurePrediction[]> = new Map();

  constructor() {
    // 启动定期分析任务
    this.startPeriodicAnalysis();
    this.logger.log('预测性维护服务已初始化 (简化版本)');
  }

  /**
   * 启动定期分析任务
   */
  private startPeriodicAnalysis(): void {
    this.logger.log('定期分析任务已启动');
  }

  /**
   * 分析设备健康状态
   */
  async analyzeDeviceHealth(data: DeviceHealthData): Promise<HealthScore> {
    try {
      // 简化的健康评分算法
      const temperatureScore = this.calculateTemperatureScore(data.temperature);
      const vibrationScore = this.calculateVibrationScore(data.vibration);
      const pressureScore = this.calculatePressureScore(data.pressure);
      const electricalScore = this.calculateElectricalScore(data.voltage, data.current);
      const mechanicalScore = this.calculateMechanicalScore(data.speed, data.torque, data.efficiency);

      const overallScore = ss.mean([
        temperatureScore,
        vibrationScore,
        pressureScore,
        electricalScore,
        mechanicalScore
      ]);

      const status = this.determineHealthStatus(overallScore);
      const recommendations = this.generateHealthRecommendations(data, overallScore);

      const healthScore: HealthScore = {
        deviceId: data.deviceId,
        score: Math.round(overallScore * 100) / 100,
        status,
        timestamp: new Date(),
        factors: {
          temperature: Math.round(temperatureScore * 100) / 100,
          vibration: Math.round(vibrationScore * 100) / 100,
          pressure: Math.round(pressureScore * 100) / 100,
          electrical: Math.round(electricalScore * 100) / 100,
          mechanical: Math.round(mechanicalScore * 100) / 100,
          overall: Math.round(overallScore * 100) / 100
        },
        recommendations
      };

      // 缓存结果
      this.healthScores.set(data.deviceId, healthScore);

      this.logger.debug(`设备 ${data.deviceId} 健康评分: ${healthScore.score} (${status})`);
      return healthScore;

    } catch (error) {
      this.logger.error(`设备健康分析失败: ${data.deviceId}`, error);
      throw new Error(`设备健康分析失败: ${error.message}`);
    }
  }

  /**
   * 预测设备故障
   */
  async predictFailures(data: DeviceHealthData): Promise<FailurePrediction[]> {
    try {
      const predictions: FailurePrediction[] = [];

      // 简化的故障预测逻辑
      if (data.temperature > 80) {
        predictions.push({
          deviceId: data.deviceId,
          failureType: FailureType.OVERHEATING,
          probability: 0.8,
          timeToFailure: 24,
          confidence: 0.85,
          timestamp: new Date(),
          description: '设备温度过高，可能导致过热故障',
          severity: 'high'
        });
      }

      if (data.vibration > 5.0) {
        predictions.push({
          deviceId: data.deviceId,
          failureType: FailureType.MECHANICAL,
          probability: 0.7,
          timeToFailure: 48,
          confidence: 0.75,
          timestamp: new Date(),
          description: '振动异常，可能存在机械故障',
          severity: 'medium'
        });
      }

      if (data.voltage < 200 || data.voltage > 250) {
        predictions.push({
          deviceId: data.deviceId,
          failureType: FailureType.ELECTRICAL,
          probability: 0.6,
          timeToFailure: 72,
          confidence: 0.7,
          timestamp: new Date(),
          description: '电压异常，可能导致电气故障',
          severity: 'medium'
        });
      }

      // 缓存预测结果
      this.predictions.set(data.deviceId, predictions);

      this.logger.debug(`设备 ${data.deviceId} 故障预测完成，发现 ${predictions.length} 个潜在故障`);
      return predictions;

    } catch (error) {
      this.logger.error(`故障预测失败: ${data.deviceId}`, error);
      throw new Error(`故障预测失败: ${error.message}`);
    }
  }

  /**
   * 生成维护建议
   */
  async generateMaintenanceRecommendations(data: DeviceHealthData): Promise<MaintenanceRecommendation[]> {
    try {
      const recommendations: MaintenanceRecommendation[] = [];
      const healthScore = await this.analyzeDeviceHealth(data);
      const predictions = await this.predictFailures(data);

      // 基于健康评分生成建议
      if (healthScore.score < 0.3) {
        recommendations.push({
          deviceId: data.deviceId,
          type: MaintenanceType.EMERGENCY,
          priority: 'urgent',
          description: '设备健康状况严重，需要立即停机检修',
          estimatedDuration: 8,
          estimatedCost: 5000,
          requiredParts: ['主要部件'],
          requiredSkills: ['高级技师'],
          scheduledDate: new Date(),
          deadline: moment().add(1, 'day').toDate(),
          riskIfDelayed: '设备可能完全损坏，造成重大损失'
        });
      } else if (healthScore.score < 0.6) {
        recommendations.push({
          deviceId: data.deviceId,
          type: MaintenanceType.PREDICTIVE,
          priority: 'high',
          description: '设备健康状况较差，建议安排预防性维护',
          estimatedDuration: 4,
          estimatedCost: 2000,
          requiredParts: ['常用备件'],
          requiredSkills: ['中级技师'],
          scheduledDate: moment().add(3, 'days').toDate(),
          deadline: moment().add(1, 'week').toDate(),
          riskIfDelayed: '设备性能继续下降，可能导致故障'
        });
      }

      // 基于故障预测生成建议
      for (const prediction of predictions) {
        if (prediction.probability > 0.7) {
          recommendations.push({
            deviceId: data.deviceId,
            type: MaintenanceType.PREDICTIVE,
            priority: prediction.severity === 'high' ? 'urgent' : 'high',
            description: `预防 ${prediction.failureType} 故障的维护`,
            estimatedDuration: 2,
            estimatedCost: 1000,
            requiredParts: ['相关备件'],
            requiredSkills: ['专业技师'],
            scheduledDate: moment().add(1, 'day').toDate(),
            deadline: moment().add(prediction.timeToFailure, 'hours').toDate(),
            riskIfDelayed: prediction.description
          });
        }
      }

      this.logger.debug(`设备 ${data.deviceId} 生成 ${recommendations.length} 个维护建议`);
      return recommendations;

    } catch (error) {
      this.logger.error(`维护建议生成失败: ${data.deviceId}`, error);
      throw new Error(`维护建议生成失败: ${error.message}`);
    }
  }

  // 私有辅助方法
  private calculateTemperatureScore(temperature: number): number {
    if (temperature < 20 || temperature > 100) return 0;
    if (temperature > 80) return 0.3;
    if (temperature > 60) return 0.7;
    return 1.0;
  }

  private calculateVibrationScore(vibration: number): number {
    if (vibration > 10) return 0;
    if (vibration > 5) return 0.3;
    if (vibration > 2) return 0.7;
    return 1.0;
  }

  private calculatePressureScore(pressure: number): number {
    if (pressure < 0.5 || pressure > 10) return 0;
    if (pressure < 1 || pressure > 8) return 0.5;
    return 1.0;
  }

  private calculateElectricalScore(voltage: number, current: number): number {
    const voltageScore = (voltage >= 200 && voltage <= 250) ? 1.0 : 0.5;
    const currentScore = (current >= 0 && current <= 100) ? 1.0 : 0.5;
    return (voltageScore + currentScore) / 2;
  }

  private calculateMechanicalScore(speed: number, torque: number, efficiency: number): number {
    const speedScore = (speed >= 0 && speed <= 3000) ? 1.0 : 0.5;
    const torqueScore = (torque >= 0 && torque <= 1000) ? 1.0 : 0.5;
    const efficiencyScore = efficiency / 100;
    return (speedScore + torqueScore + efficiencyScore) / 3;
  }

  private determineHealthStatus(score: number): HealthStatus {
    if (score >= 0.9) return HealthStatus.EXCELLENT;
    if (score >= 0.7) return HealthStatus.GOOD;
    if (score >= 0.5) return HealthStatus.FAIR;
    if (score >= 0.3) return HealthStatus.POOR;
    return HealthStatus.CRITICAL;
  }

  private generateHealthRecommendations(data: DeviceHealthData, score: number): string[] {
    const recommendations: string[] = [];
    
    if (data.temperature > 70) {
      recommendations.push('检查冷却系统');
    }
    if (data.vibration > 3) {
      recommendations.push('检查机械部件对齐');
    }
    if (score < 0.5) {
      recommendations.push('安排全面检修');
    }
    
    return recommendations;
  }

  /**
   * 定期健康检查任务
   */
  @Cron(CronExpression.EVERY_HOUR)
  async performPeriodicHealthCheck(): Promise<void> {
    this.logger.debug('执行定期健康检查');
    // 这里可以添加定期检查逻辑
  }
}
